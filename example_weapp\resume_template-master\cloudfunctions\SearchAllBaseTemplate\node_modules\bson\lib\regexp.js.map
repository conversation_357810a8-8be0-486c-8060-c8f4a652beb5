{"version": 3, "file": "regexp.js", "sourceRoot": "", "sources": ["../src/regexp.ts"], "names": [], "mappings": ";;;AAAA,iCAAmD;AAGnD,SAAS,WAAW,CAAC,GAAW;IAC9B,OAAO,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACvC,CAAC;AAgBD;;;;GAIG;AACH;IAKE;;;OAGG;IACH,oBAAY,OAAe,EAAE,OAAgB;QAC3C,IAAI,CAAC,CAAC,IAAI,YAAY,UAAU,CAAC;YAAE,OAAO,IAAI,UAAU,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAE3E,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,OAAO,GAAG,WAAW,CAAC,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,EAAE,CAAC,CAAC;QAE1C,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;YACvC,MAAM,IAAI,iBAAS,CACjB,gEAAyD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAE,CACxF,CAAC;SACH;QACD,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;YACvC,MAAM,IAAI,iBAAS,CACjB,+DAAwD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAE,CACvF,CAAC;SACH;QAED,mBAAmB;QACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC5C,IACE,CAAC,CACC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG;gBACvB,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG;gBACvB,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG;gBACvB,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG;gBACvB,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG;gBACvB,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,CACxB,EACD;gBACA,MAAM,IAAI,iBAAS,CAAC,yCAAkC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,uBAAoB,CAAC,CAAC;aAC5F;SACF;IACH,CAAC;IAEM,uBAAY,GAAnB,UAAoB,OAAgB;QAClC,OAAO,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAC1D,CAAC;IAED,gBAAgB;IAChB,mCAAc,GAAd,UAAe,OAAsB;QACnC,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QACxB,IAAI,OAAO,CAAC,MAAM,EAAE;YAClB,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC;SACzD;QACD,OAAO,EAAE,kBAAkB,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC;IAClF,CAAC;IAED,gBAAgB;IACT,2BAAgB,GAAvB,UAAwB,GAAkD;QACxE,IAAI,QAAQ,IAAI,GAAG,EAAE;YACnB,IAAI,OAAO,GAAG,CAAC,MAAM,KAAK,QAAQ,EAAE;gBAClC,qEAAqE;gBACrE,IAAI,GAAG,CAAC,MAAM,CAAC,SAAS,KAAK,YAAY,EAAE;oBACzC,OAAO,GAA4B,CAAC;iBACrC;aACF;iBAAM;gBACL,OAAO,IAAI,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;aAC1E;SACF;QACD,IAAI,oBAAoB,IAAI,GAAG,EAAE;YAC/B,OAAO,IAAI,UAAU,CACnB,GAAG,CAAC,kBAAkB,CAAC,OAAO,EAC9B,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,kBAAkB,CAAC,OAAO,CAAC,CACxD,CAAC;SACH;QACD,MAAM,IAAI,qBAAa,CAAC,mDAA4C,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAE,CAAC,CAAC;IAC7F,CAAC;IACH,iBAAC;AAAD,CAAC,AA5ED,IA4EC;AA5EY,gCAAU;AA8EvB,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,SAAS,EAAE,WAAW,EAAE,EAAE,KAAK,EAAE,YAAY,EAAE,CAAC,CAAC"}