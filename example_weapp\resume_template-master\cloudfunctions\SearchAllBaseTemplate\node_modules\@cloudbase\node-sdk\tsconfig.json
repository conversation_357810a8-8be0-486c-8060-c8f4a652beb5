{
    "compileOnSave": true,
    "compilerOptions": {
        "experimentalDecorators": true,
        "module": "commonjs",
        "target": "ES2017",
        "moduleResolution": "node",
        "outDir": "lib",
        "removeComments": false,
        "types": ["node"],
        "esModuleInterop": true,
        // "declaration": true,
        // "declarationDir": "./types",
        "resolveJsonModule": true,
        "newLine": "lf"
    },
    "include": ["src/**/*"],
    "exclude": ["node_modules", "test"]
}
